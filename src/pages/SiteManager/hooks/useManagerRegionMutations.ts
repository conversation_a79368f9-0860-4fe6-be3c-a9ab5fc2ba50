/**
 * Manager Region mutation hooks for update and delete operations
 */

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@chakra-ui/react';
import { ManagerService } from '../services/manager.service';
import { QUERY_KEYS } from '../constants/manager.constants';

export interface UpdateRegionPayload {
  manager_instance: string;
  old_region_code: string; // Original region code for API endpoint
  new_region_code: string; // New region code in payload
  region_name: string;
}

export interface AddRegionPayload {
  manager_instance: string;
  region_code: string;
}

export interface DeleteRegionPayload {
  manager_instance: string;
  region_code: string;
}

/**
 * Hook for adding manager region
 */
export const useAddManagerRegion = () => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: async (payload: AddRegionPayload) => {
      const response = await ManagerService.addRegionToManager(payload.manager_instance, payload.region_code);
      return response;
    },

    onSuccess: (result, variables) => {
      // this will update the modal
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.MANAGER_BY_ID(variables.manager_instance),
      });

      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.MANAGERS,
      });

      toast({
        title: 'Region Added',
        description: `Successfully added region ${variables.region_code} to manager ${variables.manager_instance}`,
        status: 'success',
        duration: 5000,
        isClosable: true,
        position: 'top',
        variant: 'subtle',
        colorScheme: 'green',
      });
    },

    onError: (error: any, variables) => {
      const errorMessage = error?.response?.data?.message || error?.message || 'Failed to add region';

      toast({
        title: 'Add Region Failed',
        description: `Failed to add region ${variables.region_code}: ${errorMessage}`,
        status: 'error',
        duration: 8000,
        isClosable: true,
        position: 'top',
        variant: 'subtle',
        colorScheme: 'red',
      });
    },
  });
};

/**
 * Hook for deleting manager region
 */
export const useDeleteManagerRegion = () => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: async (payload: DeleteRegionPayload) => {
      const response = await ManagerService.deleteManagerRegion(payload.manager_instance, payload.region_code);
      return response;
    },

    onSuccess: (result, variables) => {
      // this will update the modal
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.MANAGER_BY_ID(variables.manager_instance),
      });

      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.MANAGERS,
      });

      toast({
        title: 'Region Deleted',
        description: `Successfully removed region ${variables.region_code} from manager ${variables.manager_instance}`,
        status: 'success',
        duration: 5000,
        isClosable: true,
        position: 'top',
        variant: 'subtle',
        colorScheme: 'green',
      });
    },
    onError: (error: any, variables) => {
      const errorMessage = error?.response?.data?.message || error?.message || 'Failed to delete region';

      toast({
        title: 'Delete Region Failed',
        description: `Failed to delete region ${variables.region_code}: ${errorMessage}`,
        status: 'error',
        duration: 8000,
        isClosable: true,
        position: 'top',
        variant: 'subtle',
      });
    },
  });
};
