import {
  Box,
  Button,
  Divider,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  Input,
  Select,
  Stack,
  Textarea,
  useColorModeValue,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { FieldValues, useForm } from 'react-hook-form';
import { z } from 'zod';

import * as React from 'react';
import Loader from '../../../../components/loader/Loader';
import { Site } from '../../../../types/InventoryManager.type';
import { compactObject } from '../../../../utils/helpers';
import usePostSite from '../../hooks/usePostSite';
import useRegionList from '../../hooks/useRegionList';
import useUpdateSiteById from '../../hooks/useUpdateSiteById';
import { postSiteSchema } from '../../schema';

type FormData = z.infer<typeof postSiteSchema>;

type CreateSiteFormProps = {
  onClose: () => void;
  isEdit?: boolean;
  defaultSiteValues?: Site & { region: string };
};

export default function CreateSiteForm({ onClose, isEdit = false, defaultSiteValues, ...rest }: CreateSiteFormProps) {
  const { data: regionList = [] } = useRegionList();
  const createSite = usePostSite();
  const updateSite = useUpdateSiteById();

  const {
    register,
    handleSubmit,
    getValues,
    formState: { errors, isSubmitting, isDirty },
  } = useForm<FormData | (Site & { region: string })>({
    resolver: zodResolver(postSiteSchema),
    defaultValues: isEdit
      ? {
          ...defaultSiteValues,
          latitude: String(defaultSiteValues?.latitude),
          longitude: String(defaultSiteValues?.longitude),
          additional_info: defaultSiteValues?.additional_info || '',
          description: defaultSiteValues?.description || '',
        }
      : {},
  });

  function onSubmit(values: FieldValues) {
    const postSite = {
      ...values,
      region_code: values.region.split('-')[0],
      country_code: values.region.split('-')[1],
    };

    if (isEdit) {
      updateSite.mutate({
        site_id: defaultSiteValues?.site_id as number,
        site: values as Omit<Site, 'region_id'>,
      });
      return;
    }
    const postSiteObj = compactObject(postSite) as FormData;
    createSite.mutate(postSiteObj);
  }

  React.useEffect(() => {
    (createSite?.isSuccess || updateSite?.isSuccess) && onClose();
  }, [createSite, updateSite, onClose]);

  return (
    <Box
      as="form"
      bg="bg-surface"
      boxShadow={useColorModeValue('sm', 'sm-dark')}
      borderRadius="lg"
      onSubmit={handleSubmit(onSubmit)}
      {...rest}
    >
      <Stack
        spacing="5"
        px={{
          base: '4',
          md: '6',
        }}
        py={{
          base: '5',
          md: '6',
        }}
      >
        <Heading size={'md'}>{isEdit ? `Update ${getValues('name')}` : 'Create new site'}</Heading>
        <Divider />
        {isSubmitting ? (
          <Loader>Submitting...</Loader>
        ) : (
          <>
            {' '}
            <Stack
              spacing="6"
              direction={{
                base: 'column',
                md: 'row',
              }}
            >
              <Stack
                spacing="6"
                direction={{
                  base: 'column',
                  md: 'row',
                }}
              >
                <FormControl isInvalid={!!errors.name}>
                  <FormLabel htmlFor="name">Site name*</FormLabel>
                  <Input id="name" placeholder="Enter site name" {...register('name')} />
                  <FormErrorMessage>{errors?.name && errors?.name?.message}</FormErrorMessage>
                </FormControl>
                <FormControl isInvalid={!!errors.region}>
                  <FormLabel htmlFor="region">Region*</FormLabel>
                  <Select id="region" placeholder="Select region" isDisabled={isEdit} {...register('region')}>
                    {regionList.map(({ region_id, region_code, region_name, country_code, country_name }) => {
                      return (
                        <option key={region_id} value={`${region_code}-${country_code}`}>
                          {region_code} - {country_name}
                        </option>
                      );
                    })}
                  </Select>

                  <FormErrorMessage>{errors?.region && errors?.region?.message}</FormErrorMessage>
                </FormControl>
              </Stack>
            </Stack>
            <Stack
              spacing="6"
              direction={{
                base: 'column',
                md: 'row',
              }}
            >
              <FormControl isInvalid={!!errors.latitude}>
                <FormLabel htmlFor="latitude">Latitude*</FormLabel>
                <Input id="latitude" placeholder="0.000000" {...register('latitude')} />
                <FormErrorMessage>{errors?.latitude && errors?.latitude?.message}</FormErrorMessage>
              </FormControl>
              <FormControl isInvalid={!!errors.longitude}>
                <FormLabel htmlFor="longitude">Longitude*</FormLabel>
                <Input id="longitude" placeholder="0.000000" {...register('longitude')} />
                <FormErrorMessage>{errors?.longitude && errors?.longitude?.message}</FormErrorMessage>
              </FormControl>
            </Stack>
            <FormControl isInvalid={!!errors.address}>
              <FormLabel htmlFor="address">Site address*</FormLabel>
              <Input id="address" placeholder="Enter site address" {...register('address')} />
              <FormErrorMessage>{errors?.address && errors?.address?.message}</FormErrorMessage>
            </FormControl>
            <FormControl isInvalid={!!errors.description}>
              <FormLabel htmlFor="description">Description</FormLabel>
              <Textarea id="description" placeholder="Enter description" {...register('description')} size="sm" />
              <FormErrorMessage>{errors?.description && errors?.description?.message}</FormErrorMessage>
            </FormControl>
            <FormControl isInvalid={!!errors.additional_info}>
              <FormLabel htmlFor="aditionalInfo">Additional Information</FormLabel>
              <Textarea
                id="aditionalInfo"
                placeholder="Enter additional information"
                {...register('additional_info')}
                size="sm"
              />
              <FormErrorMessage>{errors?.additional_info && errors?.additional_info?.message}</FormErrorMessage>
            </FormControl>
          </>
        )}
      </Stack>
      <Divider />
      <Flex
        direction="row-reverse"
        py="4"
        px={{
          base: '4',
          md: '6',
        }}
      >
        <Button type="submit" variant="primary" isDisabled={!isDirty || isSubmitting}>
          {isEdit ? 'Update' : 'Save'}
        </Button>
      </Flex>
    </Box>
  );
}
