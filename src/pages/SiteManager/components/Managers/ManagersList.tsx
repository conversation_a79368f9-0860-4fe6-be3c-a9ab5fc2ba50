/**
 * Managers List component - Clean, modular, and maintainable
 */

import React, { useMemo, useState } from 'react';
import { Text, Button, Flex, Icon, useDisclosure } from '@chakra-ui/react';
import { AddIcon } from '@chakra-ui/icons';
import { ErrorBoundary } from 'react-error-boundary';
import { useManagersData } from '../../hooks/useManagerData';
import useManagerColumns, { FlattenedManagerProps } from '../../hooks/useManagerColumns';
import {
  ErrorBoundaryFallback,
  ErrorBoundaryLogError,
} from '../../../../components/errorComponents/ErrorBoundaryFallback';
import Loader from '../../../../components/loader/Loader';
import { ManagerProps } from '../../../../types/InventoryManager.type';
import NodeComponents from '../../../CellOverview/NodeComponents';
import { DataTable } from '../../../MetricsCollector/components/DataTable';
import { useDeleteManager } from '../../hooks/useManagerMutations';
import { ManagerFormModal } from '../ManagerForm/ManagerFormModal';

/**
 * Main Managers list component with enhanced structure
 */
const ManagersList: React.FC = () => {
  // Data fetching
  const { data: managersData, isLoading, error } = useManagersData();

  // Modal state management
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [editingManager, setEditingManager] = useState<FlattenedManagerProps | undefined>();

  // Mutations
  const deleteManagerMutation = useDeleteManager();

  // Transform data for table display
  const flattenedData = useMemo<FlattenedManagerProps[]>(() => {
    if (!managersData) return [];

    return managersData.map((item: ManagerProps) => ({
      manager_instance: item.manager.manager_instance,
      component_type: item.manager.component_type,
      manager_type: item.manager.manager_type,
      manager_url: item.manager.manager_url,
      version: item.manager.version,
      ran_type: item.manager.ran_type,
      host_id: item.host?.node_id || null,
      host_type: item.host?.node_type || null,
      host_node_id: item.host?.node_serial_no || null,
      region_codes: item.regions.map((r) => r.region_code).join(', '),
      region_names: item.regions.map((r) => r.region_name).join(', '),
      regions: item.regions,
      status: item.host?.status,
    }));
  }, [managersData]);

  // Event handlers
  const handleOpenCreateManager = () => {
    setEditingManager(undefined);
    onOpen();
  };

  const handleEditManager = (manager: FlattenedManagerProps) => {
    setEditingManager(manager);
    onOpen();
  };

  const handleDeleteManager = async (manager: FlattenedManagerProps) => {
    deleteManagerMutation.mutate(manager.manager_instance);
  };

  const handleCloseModal = () => {
    setEditingManager(undefined);
    onClose();
  };

  const handleManagerSuccess = (updatedManager?: any) => {
    // The query invalidation in mutations will handle data refresh automatically
    console.log('Manager operation successful:', updatedManager);
  };

  // Table columns configuration
  const columns = useManagerColumns({
    onEdit: handleEditManager,
    onDelete: handleDeleteManager,
  });

  // Sub-component renderer for expandable rows
  const renderSubComponent = (props: any) => {
    if (!props.row.original.host_node_id || !props.row.original.host_type) {
      return (
        <Text p={4} color="muted">
          No host node available for this manager
        </Text>
      );
    }

    const modifiedProps = {
      ...props,
      row: {
        ...props.row,
        original: {
          ...props.row.original,
          node_id: props.row.original.host_node_id, // Map host_node_id to node_id
        },
      },
    };

    return (
      <NodeComponents
        row={modifiedProps}
        node={{} as any} // Not used when caller="Nodes"
        nodeOpen={true}
        caller="Nodes"
        nodeType={props.row.original.host_type}
      />
    );
  };

  // Loading state
  if (isLoading) {
    return <Loader />;
  }

  // Error state
  if (error) {
    return (
      <Text color="red.500" textAlign="center">
        Error loading managers data: {error.message}
      </Text>
    );
  }

  return (
    <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
      <Flex direction="column" gap={4}>
        {/* Header with Add Manager Button */}
        <Flex justify="flex-end" align="flex-end">
          <Button
            data-testid="add-manager-button"
            variant="primary"
            leftIcon={<Icon as={AddIcon} marginStart="-1" />}
            onClick={handleOpenCreateManager}
            isDisabled={deleteManagerMutation.isLoading}
          >
            Add Manager
          </Button>
        </Flex>

        {/* Managers Data Table */}
        <DataTable
          isExpandable={true}
          enableFilter={true}
          columns={columns}
          data={flattenedData}
          isLoading={isLoading}
          defaultPageSize={100}
          renderSubComponent={renderSubComponent}
          hasEmptyResult={flattenedData?.length === 0}
          version="v2"
        />

        {/* Create/Edit Manager Modal */}
        <ManagerFormModal
          isOpen={isOpen}
          onClose={handleCloseModal}
          isEdit={!!editingManager}
          defaultValues={editingManager}
          onSuccess={handleManagerSuccess}
        />
      </Flex>
    </ErrorBoundary>
  );
};

export default ManagersList;
