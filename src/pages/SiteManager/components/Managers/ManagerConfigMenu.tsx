import React from 'react';
import {
  IconButton,
  <PERSON>u,
  MenuButton,
  MenuItem,
  MenuList,
  <PERSON>dal,
  ModalBody,
  ModalCloseButton,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  ModalOverlay,
  Button,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { BsThreeDots } from 'react-icons/bs';
import { EditIcon, DeleteIcon } from '@chakra-ui/icons';
import { FiMapPin } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../../data/constants';
import useLogin from '../../../../hooks/useLogin';
import { FlattenedManagerProps } from '../../hooks/useManagerColumns';
import { UpdateRegionModal } from '../UpdateRegionModal';

export interface ManagerConfigMenuProps {
  manager: FlattenedManagerProps;
  onEdit?: (manager: FlattenedManagerProps) => void;
  onDelete?: (manager: FlattenedManagerProps) => Promise<void>;
  onUpdateRegion?: (manager: FlattenedManagerProps) => void;
  dataTestId?: string;
  isDeleting?: boolean;
}

const ManagerConfigMenu: React.FC<ManagerConfigMenuProps> = ({
  manager,
  onEdit,
  onDelete,
  onUpdateRegion,
  dataTestId,
  isDeleting = false,
}) => {
  const navigate = useNavigate();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isUpdateRegionOpen, onOpen: onUpdateRegionOpen, onClose: onUpdateRegionClose } = useDisclosure();
  const [isDeleteSubmitting, setIsDeleteSubmitting] = React.useState(false);

  // Check user permissions
  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);

  const handleEditManager = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (onEdit) {
      onEdit(manager);
    } else {
      navigate('/site-manager', {
        state: {
          editManager: manager,
          mode: 'edit',
        },
      });
    }
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onOpen();
  };

  const handleConfirmDelete = async () => {
    if (!onDelete) {
      onClose();
      return;
    }

    setIsDeleteSubmitting(true);
    try {
      await onDelete(manager);
      onClose();
    } catch (error) {
      console.error('Failed to delete manager:', error);
      // Keep modal open to show error state
    } finally {
      setIsDeleteSubmitting(false);
    }
  };

  const handleCancelDelete = () => {
    onClose();
  };

  const handleUpdateRegionClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (onUpdateRegion) {
      onUpdateRegion(manager);
    } else {
      onUpdateRegionOpen();
    }
  };

  // Check if manager has regions (but show menu option regardless)
  const hasRegions = manager.regions && manager.regions.length > 0;

  if (!checkRoleAccess) {
    return null;
  }

  return (
    <>
      <Menu>
        <MenuButton
          data-testid={dataTestId}
          onClick={(e) => {
            e.stopPropagation();
          }}
          as={IconButton}
          aria-label="Manager Options"
          icon={<BsThreeDots />}
          variant="outline"
          size="sm"
          isDisabled={isDeleting}
        />
        <MenuList minWidth="160px">
          <MenuItem data-testid="edit-manager" onClick={handleEditManager} isDisabled={isDeleting}>
            <EditIcon mr="1rem" />
            Update Manager
          </MenuItem>

          <MenuItem
            data-testid="update-region"
            onClick={handleUpdateRegionClick}
            isDisabled={isDeleting}
            _hover={{
              bg: 'blue.50',
              color: 'blue.600',
            }}
          >
            <FiMapPin style={{ marginRight: '1rem' }} />
            Update Region
          </MenuItem>

          <MenuItem
            data-testid="delete-manager"
            onClick={handleDeleteClick}
            isDisabled={isDeleting}
            color="red.500"
            _hover={{
              bg: 'red.50',
              color: 'red.600',
            }}
          >
            <DeleteIcon mr="1rem" />
            Delete Manager
          </MenuItem>
        </MenuList>
      </Menu>

      {/* Delete Confirmation Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="md">
        <ModalOverlay />
        <ModalContent data-testid="delete-manager-modal">
          <ModalHeader>Delete Manager</ModalHeader>
          <ModalCloseButton isDisabled={isDeleteSubmitting} />
          <ModalBody>
            <Text fontWeight="semibold" mb={4}>
              Are you sure you want to delete this manager?
            </Text>
            <Text mb={2}>
              <strong>Manager Instance:</strong> {manager.manager_instance}
            </Text>
            <Text mb={2}>
              <strong>Manager Type:</strong> {manager.manager_type}
            </Text>
            <Text mb={2}>
              <strong>Component Type:</strong> {manager.component_type}
            </Text>
            <Text mb={4}>
              <strong>Manager URL:</strong> {manager.manager_url}
            </Text>
            <Text
              mt={4}
              p={3}
              bg="red.50"
              border="1px"
              borderColor="red.200"
              borderRadius="md"
              color="red.700"
              fontSize="sm"
            >
              <strong>Warning:</strong> This action cannot be undone. Deleting this manager will remove all its
              configuration data and may affect related network components.
            </Text>
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="gray" mr={3} onClick={handleCancelDelete} isDisabled={isDeleteSubmitting}>
              Cancel
            </Button>
            <Button
              colorScheme="red"
              onClick={handleConfirmDelete}
              isLoading={isDeleteSubmitting}
              loadingText="Deleting..."
              data-testid="confirm-delete-manager"
            >
              Delete Manager
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Update Region Modal */}
      <UpdateRegionModal isOpen={isUpdateRegionOpen} onClose={onUpdateRegionClose} manager={manager} />
    </>
  );
};

export default ManagerConfigMenu;
